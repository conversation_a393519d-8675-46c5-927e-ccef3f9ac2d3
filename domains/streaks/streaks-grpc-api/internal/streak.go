package internal

import (
	"context"
	"fmt"
	"sort"
	"time"

	"cloud.google.com/go/civil"
	gS "cloud.google.com/go/spanner"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	pbPost "github.com/BeReal-App/backend-go/proto/private/post/v2"
	"github.com/BeReal-App/backend-go/shared/data"
	"github.com/BeReal-App/backend-go/shared/model"
	"github.com/BeReal-App/backend-go/shared/scope"
	"github.com/rs/zerolog"
)

// CalculateStreakRecovery calculates what the user's streak would be if missing days were filled
func (s *Server) CalculateStreakRecovery(ctx context.Context, req *pbUser.CalculateStreakRecoveryRequest) (*pbUser.CalculateStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "CalculateStreakRecovery")

	l.Info().
		Str("user_id", req.UserId).
		Int64("number_of_days", int64(req.NumberOfDays)).
		Msg("Starting streak recovery calculation")

	// Validate and normalize input
	numberOfDays := s.validateAndNormalizeNumberOfDays(uint32(req.NumberOfDays), &l, req.UserId)

	// Fetch all required data
	data, err := s.fetchStreakRecoveryData(ctx, req.UserId, &l)
	if err != nil {
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: err.Error(),
			},
		}, nil
	}

	// Calculate streak recovery using business logic
	calculation := s.calculateStreakRecoveryLogic(ctx, req.UserId, numberOfDays, data, &l)

	return &pbUser.CalculateStreakRecoveryResponse{
		Calculation: calculation,
	}, nil
}

// StreakRecoveryData holds all the data needed for streak recovery calculation
type StreakRecoveryData struct {
	UserBasicInfo *pbUser.UserBasicInfo
	CurrentStreak *pbUser.Streak
	UserRegion    model.Region
	SortedMoments []models.Moment
	UserPosts     []UserPost
}

// validateAndNormalizeNumberOfDays validates and normalizes the number of days input
func (s *Server) validateAndNormalizeNumberOfDays(numberOfDays uint32, l *zerolog.Logger, userID string) int {
	days := int(numberOfDays)
	if days <= 0 {
		l.Debug().
			Str("user_id", userID).
			Int64("requested_days", int64(numberOfDays)).
			Int("default_days", 30).
			Msg("Invalid number of days provided, using default")
		days = 30
	}
	return days
}

// fetchStreakRecoveryData fetches all external data needed for streak recovery calculation
func (s *Server) fetchStreakRecoveryData(ctx context.Context, userID string, l *zerolog.Logger) (*StreakRecoveryData, error) {
	// Get user's basic info to determine region
	l.Debug().Str("user_id", userID).Msg("Fetching user basic info")
	userBasicInfo, err := s.getUserBasicInfo(ctx, userID)
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get user basic info")
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}
	l.Debug().
		Str("user_id", userID).
		Str("region", userBasicInfo.Region).
		Msg("Retrieved user basic info")

	// Get current streak
	l.Debug().Str("user_id", userID).Msg("Fetching current streak")
	currentStreak, err := s.userServiceClient.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: userID})
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get current streak")
		return nil, fmt.Errorf("failed to get current streak: %v", err)
	}
	l.Debug().
		Str("user_id", userID).
		Uint64("current_streak", currentStreak.Streak.Length).
		Msg("Retrieved current streak")

	// Parse user's region
	userRegion := model.NewRegion(userBasicInfo.Region)
	if userRegion == model.RegionUnknown {
		l.Error().
			Str("user_id", userID).
			Str("region", userBasicInfo.Region).
			Msg("Invalid user region")
		return nil, fmt.Errorf("invalid user region")
	}
	l.Debug().
		Str("user_id", userID).
		Str("region", userRegion.String()).
		Msg("Parsed user region")

	// Get moments for user's region
	sortedMoments, err := s.getAndProcessMomentsForRegion(userID, userRegion, l)
	if err != nil {
		return nil, err
	}

	// Get all user's posts
	l.Debug().Str("user_id", userID).Msg("Fetching all user posts")
	userPosts, err := s.getAllUserPosts(ctx, userID)
	if err != nil {
		l.Warn().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get user posts, continuing with empty slice")
		userPosts = []UserPost{}
	}
	l.Debug().
		Str("user_id", userID).
		Int("posts_count", len(userPosts)).
		Msg("Retrieved user posts")

	return &StreakRecoveryData{
		UserBasicInfo: userBasicInfo,
		CurrentStreak: currentStreak.Streak,
		UserRegion:    userRegion,
		SortedMoments: sortedMoments,
		UserPosts:     userPosts,
	}, nil
}

// getAndProcessMomentsForRegion fetches and processes moments for a specific region
func (s *Server) getAndProcessMomentsForRegion(userID string, userRegion model.Region, l *zerolog.Logger) ([]models.Moment, error) {
	// Get all moments for the user's region
	l.Debug().
		Str("user_id", userID).
		Str("region", userRegion.String()).
		Msg("Fetching moments for user region")
	allMomentsByRegion := s.momentsClient.GetAllByRegion()
	userRegionMoments, exists := allMomentsByRegion[userRegion]
	if !exists || userRegionMoments.Len() == 0 {
		l.Error().
			Str("user_id", userID).
			Str("region", userRegion.String()).
			Bool("exists", exists).
			Int("moments_count", userRegionMoments.Len()).
			Msg("No moments found for user region")
		return nil, fmt.Errorf("no moments found for user region")
	}
	l.Debug().
		Str("user_id", userID).
		Str("region", userRegion.String()).
		Int("moments_count", userRegionMoments.Len()).
		Msg("Retrieved moments for user region")

	// Sort moments by FiredAt desc (most recent first)
	return s.sortAndFilterMoments(userRegionMoments, userID, l), nil
}

// sortAndFilterMoments sorts moments by FiredAt descending and filters out unfired moments
func (s *Server) sortAndFilterMoments(userRegionMoments *data.ImmutableSlice[models.Moment], userID string, l *zerolog.Logger) []models.Moment {
	userRegionMomentsList := userRegionMoments.AsMutableSlice()
	l.Debug().
		Str("user_id", userID).
		Int("total_moments", len(userRegionMomentsList)).
		Msg("Processing moments for sorting")

	sortedMoments := make([]models.Moment, 0, userRegionMoments.Len())
	var unfiredCount int
	for _, moment := range userRegionMomentsList {
		// Only include fired moments
		if moment.FiredAt != nil {
			sortedMoments = append(sortedMoments, moment)
		} else {
			unfiredCount++
		}
	}
	l.Debug().
		Str("user_id", userID).
		Int("fired_moments", len(sortedMoments)).
		Int("unfired_moments", unfiredCount).
		Msg("Filtered out unfired moments")

	sort.Slice(sortedMoments, func(i, j int) bool {
		return sortedMoments[i].FiredAt.After(*sortedMoments[j].FiredAt)
	})
	l.Debug().
		Str("user_id", userID).
		Int("sorted_moments", len(sortedMoments)).
		Msg("Sorted moments by FiredAt (descending)")

	return sortedMoments
}

// calculateStreakRecoveryLogic performs the core business logic for streak recovery calculation
func (s *Server) calculateStreakRecoveryLogic(ctx context.Context, userID string, numberOfDays int, data *StreakRecoveryData, l *zerolog.Logger) *pbUser.StreakRecoveryCalculation {
	// Create a map of moment IDs to check if user posted
	postsByMomentID := s.createPostsByMomentIDMap(data.UserPosts, userID, l)

	// Find gaps in the last N days
	gapsToFill := s.findStreakGaps(data.SortedMoments, postsByMomentID, numberOfDays, userID, l)

	// Sort gaps by Date descending (most recent first)
	sort.Slice(gapsToFill, func(i, j int) bool {
		dateI := time.Date(int(gapsToFill[i].Date.Year), time.Month(gapsToFill[i].Date.Month), int(gapsToFill[i].Date.Day), 0, 0, 0, 0, time.UTC)
		dateJ := time.Date(int(gapsToFill[j].Date.Year), time.Month(gapsToFill[j].Date.Month), int(gapsToFill[j].Date.Day), 0, 0, 0, 0, time.UTC)
		return dateI.After(dateJ)
	})

	// Calculate estimated streak after filling gaps
	l.Debug().
		Str("user_id", userID).
		Int("gaps_to_fill", len(gapsToFill)).
		Msg("Calculating estimated streak")
	estimatedStreak := s.calculateEstimatedStreak(ctx, userID, data.SortedMoments, postsByMomentID, gapsToFill)

	isEligible := len(gapsToFill) > 0 && len(gapsToFill) <= 10

	l.Info().
		Str("user_id", userID).
		Uint64("current_streak", data.CurrentStreak.Length).
		Uint64("estimated_streak", estimatedStreak).
		Int("gaps_count", len(gapsToFill)).
		Bool("is_eligible", isEligible).
		Msg("Completed streak recovery calculation")

	return &pbUser.StreakRecoveryCalculation{
		CurrentStreak:   data.CurrentStreak.Length,
		EstimatedStreak: estimatedStreak,
		GapsToFill:      gapsToFill,
		IsEligible:      isEligible,
	}
}

// createPostsByMomentIDMap creates a map for quick lookup of posts by moment ID
func (s *Server) createPostsByMomentIDMap(userPosts []UserPost, userID string, l *zerolog.Logger) map[string]bool {
	postsByMomentID := make(map[string]bool)
	for _, post := range userPosts {
		if post.MomentID.Valid {
			postsByMomentID[post.MomentID.StringVal] = true
		}
	}
	l.Debug().
		Str("user_id", userID).
		Int("unique_moment_posts", len(postsByMomentID)).
		Msg("Created map of posts by moment ID")
	return postsByMomentID
}

// findStreakGaps finds gaps in the user's posting history for the specified number of days
func (s *Server) findStreakGaps(sortedMoments []models.Moment, postsByMomentID map[string]bool, numberOfDays int, userID string, l *zerolog.Logger) []*pbUser.StreakGap {
	// Find gaps in the last N days (recovery is only allowed for recent gaps)
	now := time.Now()
	lookbackAgo := now.AddDate(0, 0, -numberOfDays)
	l.Debug().
		Str("user_id", userID).
		Time("now", now).
		Time("lookback_threshold", lookbackAgo).
		Int("lookback_days", numberOfDays).
		Msg("Calculating lookback period for gap detection")

	var gapsToFill []*pbUser.StreakGap
	// Track which dates have been processed and whether they have posts
	daysProcessed := make(map[string]bool)
	daysWithPosts := make(map[string]bool)
	dayToMomentMap := make(map[string]string) // For gap tracking, use first moment encountered for each day

	// First pass: identify all days and check if user posted to any moment on each day
	for _, moment := range sortedMoments {
		// Only consider moments from the last N days for gap filling
		if moment.FiredAt.Before(lookbackAgo) {
			continue
		}

		// Generate a date key in YYYY-MM-DD format
		dateKey := moment.FiredAt.Format("2006-01-02")

		// Track that we've seen this day
		if !daysProcessed[dateKey] {
			daysProcessed[dateKey] = true
			dayToMomentMap[dateKey] = moment.ID // Use first moment ID for this day
		}

		// Check if user posted for this moment
		if postsByMomentID[moment.ID] {
			daysWithPosts[dateKey] = true
		}
	}

	// Second pass: identify gaps (days with no posts to any moment)
	for dateKey, hasPost := range daysProcessed {
		if hasPost && !daysWithPosts[dateKey] {
			// This day has moments but no posts to any of them - it's a gap
			momentID := dayToMomentMap[dateKey]

			// Parse the date back to create the gap
			parsedTime, err := time.Parse("2006-01-02", dateKey)
			if err != nil {
				l.Error().
					Str("user_id", userID).
					Str("date_key", dateKey).
					Err(err).
					Msg("Failed to parse date key")
				continue
			}

			gapsToFill = append(gapsToFill, &pbUser.StreakGap{
				Date: &pbCommon.Date{
					Year:  int32(parsedTime.Year()),
					Month: int32(parsedTime.Month()),
					Day:   int32(parsedTime.Day()),
				},
				MomentId: momentID,
			})

			l.Debug().
				Str("user_id", userID).
				Str("moment_id", momentID).
				Str("date", dateKey).
				Msg("Found gap to fill")
		}
	}

	l.Info().
		Str("user_id", userID).
		Int("gaps_found", len(gapsToFill)).
		Msg("Identified gaps to fill for streak recovery")

	return gapsToFill
}

// ApplyStreakRecovery applies streak recovery by inserting compensation records and recomputing the streak
func (s *Server) ApplyStreakRecovery(ctx context.Context, req *pbUser.ApplyStreakRecoveryRequest) (*pbUser.ApplyStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "ApplyStreakRecovery")

	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_count", len(req.GapsToFill)).
		Msg("Starting streak recovery application")

	// Validate input
	if err := s.validateApplyStreakRecoveryRequest(req, &l); err != nil {
		return nil, err
	}

	// Execute the core streak recovery logic
	result, err := s.executeStreakRecoveryProcess(ctx, req, &l)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// validateApplyStreakRecoveryRequest validates the input for ApplyStreakRecovery
func (s *Server) validateApplyStreakRecoveryRequest(req *pbUser.ApplyStreakRecoveryRequest, l *zerolog.Logger) error {
	if req.UserId == "" {
		l.Error().Msg("Invalid user ID: empty")
		return status.Errorf(codes.InvalidArgument, "invalid user ID: empty")
	}

	if len(req.GapsToFill) == 0 {
		l.Error().
			Str("user_id", req.UserId).
			Msg("No gaps provided for recovery")
		return status.Errorf(codes.InvalidArgument, "no gaps provided for recovery")
	}

	if len(req.GapsToFill) > 10 {
		l.Error().
			Str("user_id", req.UserId).
			Int("gaps_count", len(req.GapsToFill)).
			Msg("Too many gaps for recovery (max 10)")
		return status.Errorf(codes.InvalidArgument, "too many gaps for recovery: %d (max 10)", len(req.GapsToFill))
	}

	// Log gap details for debugging
	for i, gap := range req.GapsToFill {
		logEvent := l.Debug().
			Str("user_id", req.UserId).
			Int("gap_index", i).
			Str("moment_id", gap.MomentId)

		if gap.Date != nil {
			logEvent = logEvent.
				Int32("year", gap.Date.Year).
				Int32("month", gap.Date.Month).
				Int32("day", gap.Date.Day)
		} else {
			logEvent = logEvent.Str("date", "nil")
		}

		logEvent.Msg("Gap details for filling")
	}

	return nil
}

// StreakRecoveryContext holds the data needed during streak recovery execution
type StreakRecoveryContext struct {
	UserBasicInfo *pbUser.UserBasicInfo
	CurrentStreak *pbUser.Streak
	NewStreak     *pbUser.Streak
}

// executeStreakRecoveryProcess executes the core streak recovery logic
func (s *Server) executeStreakRecoveryProcess(ctx context.Context, req *pbUser.ApplyStreakRecoveryRequest, l *zerolog.Logger) (*pbUser.ApplyStreakRecoveryResponse, error) {
	// Gather necessary data
	recoveryCtx, err := s.gatherStreakRecoveryContext(ctx, req.UserId, l)
	if err != nil {
		return nil, err
	}

	// Apply the streak compensation
	err = s.applyStreakCompensation(ctx, req.UserId, req.GapsToFill, l)
	if err != nil {
		return nil, err
	}

	// Get updated streak after compensation
	recoveryCtx.NewStreak, err = s.getUpdatedStreak(ctx, req.UserId, l)
	if err != nil {
		return nil, err
	}

	// Log success metrics
	s.logStreakRecoverySuccess(req.UserId, recoveryCtx, req.GapsToFill, l)

	return &pbUser.ApplyStreakRecoveryResponse{
		NewStreak: recoveryCtx.NewStreak,
	}, nil
}

// gatherStreakRecoveryContext collects the necessary data for streak recovery
func (s *Server) gatherStreakRecoveryContext(ctx context.Context, userID string, l *zerolog.Logger) (*StreakRecoveryContext, error) {
	// Get user's current streak before applying recovery
	currentStreak, err := s.userServiceClient.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: userID})
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get current streak before applying recovery")
	} else {
		l.Info().
			Str("user_id", userID).
			Uint64("current_streak", currentStreak.Streak.Length).
			Msg("Current streak before applying recovery")
	}

	// Get user's basic info to verify they exist
	l.Debug().
		Str("user_id", userID).
		Msg("Fetching user basic info")
	userBasicInfo, err := s.getUserBasicInfo(ctx, userID)
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get user info")
		return nil, fmt.Errorf("failed to get user info for userID %s: %w", userID, err)
	}
	l.Debug().
		Str("user_id", userID).
		Str("region", userBasicInfo.Region).
		Msg("Retrieved user basic info")

	return &StreakRecoveryContext{
		UserBasicInfo: userBasicInfo,
		CurrentStreak: func() *pbUser.Streak {
			if currentStreak != nil {
				return currentStreak.Streak
			}
			return nil
		}(),
	}, nil
}

// applyStreakCompensation inserts compensation records for the gaps
func (s *Server) applyStreakCompensation(ctx context.Context, userID string, gaps []*pbUser.StreakGap, l *zerolog.Logger) error {
	l.Debug().
		Str("user_id", userID).
		Int("gaps_count", len(gaps)).
		Msg("Inserting streak compensation records")

	err := s.insertStreakCompensationRecords(ctx, userID, gaps)
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to insert streak compensation records")
		return fmt.Errorf("failed to insert streak compensation records: %w", err)
	}

	l.Info().
		Str("user_id", userID).
		Int("gaps_filled", len(gaps)).
		Msg("Successfully inserted streak compensation records")

	return nil
}

// getUpdatedStreak retrieves the updated streak after compensation
func (s *Server) getUpdatedStreak(ctx context.Context, userID string, l *zerolog.Logger) (*pbUser.Streak, error) {
	l.Debug().
		Str("user_id", userID).
		Msg("Fetching updated streak after compensation")

	newStreakResponse, err := s.userServiceClient.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: userID})
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get updated streak after compensation")
		return nil, fmt.Errorf("failed to get updated streak after compensation: %w", err)
	}

	return newStreakResponse.Streak, nil
}

// logStreakRecoverySuccess logs the successful completion of streak recovery
func (s *Server) logStreakRecoverySuccess(userID string, recoveryCtx *StreakRecoveryContext, gaps []*pbUser.StreakGap, l *zerolog.Logger) {
	// Calculate streak difference
	var streakDifference uint64
	var oldStreakLength uint64

	if recoveryCtx.CurrentStreak != nil {
		oldStreakLength = recoveryCtx.CurrentStreak.Length
		streakDifference = recoveryCtx.NewStreak.Length - recoveryCtx.CurrentStreak.Length
	}

	l.Info().
		Str("user_id", userID).
		Uint64("old_streak_length", oldStreakLength).
		Uint64("new_streak_length", recoveryCtx.NewStreak.Length).
		Uint64("streak_increase", streakDifference).
		Str("region", recoveryCtx.UserBasicInfo.Region).
		Int("gaps_filled", len(gaps)).
		Msg("Completed streak recovery successfully")
}

// insertStreakCompensationRecords inserts multiple StreakCompensation records in a single transaction
func (s *Server) insertStreakCompensationRecords(ctx context.Context, userID string, gaps []*pbUser.StreakGap) error {
	l := scope.GetLoggerForCallsite(ctx, "insertStreakCompensationRecords")

	l.Info().
		Str("user_id", userID).
		Int("gaps_count", len(gaps)).
		Msg("Starting to insert streak compensation records")

	// Prepare mutations for batch insert
	var mutations []*gS.Mutation
	for _, gap := range gaps {
		// Convert pbCommon.Date to civil.Date for the Date column
		spannerDate := civil.Date{
			Year:  int(gap.Date.Year),
			Month: time.Month(gap.Date.Month),
			Day:   int(gap.Date.Day),
		}

		mutation := gS.InsertOrUpdate(
			"StreakCompensation",
			[]string{"UserId", "Date", "MomentID"},
			[]interface{}{userID, spannerDate, gap.MomentId},
		)
		mutations = append(mutations, mutation)

		l.Debug().
			Str("user_id", userID).
			Str("date", spannerDate.String()).
			Str("moment_id", gap.MomentId).
			Msg("Prepared StreakCompensation mutation")
	}

	l.Debug().
		Str("user_id", userID).
		Int("mutations_count", len(mutations)).
		Msg("Executing batch insert transaction")

	// Execute batch insert in a transaction
	_, err := s.spannerClient.ReadWriteTransactionWithOptions(
		ctx,
		func(_ context.Context, tx *gS.ReadWriteTransaction) error {
			return tx.BufferWrite(mutations)
		},
		gS.TransactionOptions{
			TransactionTag: "ApplyStreakRecovery",
		},
	)
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to insert StreakCompensation records")
		return fmt.Errorf("failed to insert StreakCompensation records: %w", err)
	}

	l.Info().
		Str("user_id", userID).
		Int("records_inserted", len(gaps)).
		Msg("Successfully inserted compensation records")

	return nil
}

// UserPost represents a simplified post structure for streak calculations
type UserPost struct {
	PostID    string
	UserID    string
	MomentID  gS.NullString
	CreatedAt time.Time
}

// getAllUserPosts retrieves all user posts (no time limit) via post service
func (s *Server) getAllUserPosts(ctx context.Context, userID string) ([]UserPost, error) {
	// Call the post service to get user posts
	resp, err := s.postClient.GetPostsOfUser(ctx, &pbPost.GetPostsOfUserRequest{
		UserId: userID,
		Order: &pbPost.OrderBy{
			Field:     pbPost.OrderBy_FIELD_CREATED_AT,
			Direction: pbPost.OrderBy_DIRECTION_DESC,
		},
		IncludeDeleted: func() *bool { b := false; return &b }(), // Exclude deleted posts for streak calculation
		SearchAll:      func() *bool { b := true; return &b }(),  // Search full table for all posts
		Limit:          10000,                                    // Set high limit since we need all posts for accurate streak calculation
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get user posts from post service: %w", err)
	}

	// Convert proto posts to UserPost structs
	var posts []UserPost
	for _, protoPost := range resp.Posts {
		post := UserPost{
			PostID:    protoPost.Id,
			UserID:    protoPost.UserId,
			CreatedAt: protoPost.CreatedAt.AsTime(),
		}

		// Set MomentID if available
		if protoPost.MomentId != "" {
			post.MomentID = gS.NullString{StringVal: protoPost.MomentId, Valid: true}
		} else {
			post.MomentID = gS.NullString{Valid: false}
		}

		posts = append(posts, post)
	}

	return posts, nil
}

// calculateEstimatedStreak calculates what the streak would be if gaps were filled
// This function uses all user posts (not time-limited) to determine where the streak would break
func (s *Server) calculateEstimatedStreak(_ context.Context, _ string, sortedMoments []models.Moment, postsByMomentID map[string]bool, gapsToFill []*pbUser.StreakGap) uint64 {
	// Create a map of gaps to fill for easier lookup
	gapMomentIDs := make(map[string]bool)
	for _, gap := range gapsToFill {
		gapMomentIDs[gap.MomentId] = true
	}

	// Create a map of calendar days that would have posts (either real posts or filled gaps)
	daysWithPostsOrFills := make(map[string]bool)

	// Add days where user actually posted
	for _, moment := range sortedMoments {
		if postsByMomentID[moment.ID] {
			dateKey := moment.FiredAt.Format("2006-01-02")
			daysWithPostsOrFills[dateKey] = true
		}
	}

	// Add days where gaps would be filled
	for _, moment := range sortedMoments {
		if gapMomentIDs[moment.ID] {
			dateKey := moment.FiredAt.Format("2006-01-02")
			daysWithPostsOrFills[dateKey] = true
		}
	}

	// Calculate streak from most recent moment backwards, counting calendar days
	consecutiveDays := 0

	// Start from the most recent moment and work backwards day by day
	if len(sortedMoments) == 0 {
		return 0
	}

	// Find the most recent day and work backwards
	currentTime := *sortedMoments[0].FiredAt // Most recent moment (they're sorted desc)

	for {
		dateKey := currentTime.Format("2006-01-02")

		// Check if this day would have a post (either real or filled)
		if daysWithPostsOrFills[dateKey] {
			consecutiveDays++
			// Move to previous day
			currentTime = currentTime.AddDate(0, 0, -1)
		} else {
			// Break in the streak - stop counting
			break
		}

		// Safety check to prevent infinite loop (arbitrary limit)
		if consecutiveDays > 10000 {
			break
		}
	}

	return uint64(consecutiveDays)
}

// getUserBasicInfo retrieves user basic info via user service
func (s *Server) getUserBasicInfo(ctx context.Context, userID string) (*pbUser.UserBasicInfo, error) {
	l := scope.GetLoggerForCallsite(ctx, "getUserBasicInfo")

	// Get user basic info from user service
	userBasicInfoResp, err := s.userServiceClient.GetUserBasicInfo(ctx, &pbUser.GetUserBasicInfoRequest{
		UserId: userID,
	})
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get user basic info from user service")
		return nil, err
	}

	l.Debug().
		Str("user_id", userID).
		Str("region", userBasicInfoResp.BasicInfo.Region).
		Msg("Retrieved user basic info from user service")

	return userBasicInfoResp.BasicInfo, nil
}
