{"permissions": {"allow": ["<PERSON><PERSON>(go test:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(go tool cover:*)", "<PERSON><PERSON>(go run:*)", "Bash(go build:*)", "Bash(rg:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(golangci-lint run:*)", "Bash(make lint)", "Bash(ls:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(buf generate:*)", "Bash(rm:*)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/Users/<USER>/voodoo_local.sh:*)", "Bash(PUBSUB_EMULATOR_HOST=localhost:8686 go run ./domains/entity/user-grpc-api 2 >& 1)", "Bash(PUBSUB_EMULATOR_HOST=localhost:8686 PUBSUB_PROJECT_ID=backend-core-dev go run domains/tooling/pubsub-cmd/main.go --action list_topics)", "Bash(ln:*)", "Bash(JWT_TOKEN=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJCZVJlYWwgRW5naW5lZXJzIiwiZXhwIjoxNzQ5OTc4NjgwLCJpc3MiOiJpbXBlcnNvbmF0ZSAtZ29sYW5nLSIsInVpZCI6InRlc3QtdXNlci0xMjMiLCJ1c2VyX2lkIjoidGVzdC11c2VyLTEyMyJ9.yJ76bRv6IEzIB8Xc5bwN7oDewFr8cb2h-YpfhvHeuJY\")", "Bash(grpcurl:*)", "Bash(export SPANNER_EMULATOR_HOST=localhost:9010)", "Bash(export SPANNER_EMULATOR_HOST=localhost:9011)", "<PERSON><PERSON>(timeout:*)", "Bash(SPANNER_EMULATOR_HOST=localhost:9011 go run main.go)", "<PERSON><PERSON>(pkill:*)", "Bash(awk:*)", "Bash(buf lint:*)", "<PERSON><PERSON>(mv:*)", "Bash(go mod:*)"], "deny": []}}